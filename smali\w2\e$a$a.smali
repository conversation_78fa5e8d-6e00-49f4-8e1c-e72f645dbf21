.class Lw2/e$a$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ln4/f;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lw2/e$a;->a(Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic a:Lorg/json/JSONObject;

.field final synthetic b:Lw2/e$a;


# direct methods
.method constructor <init>(Lw2/e$a;Lorg/json/JSONObject;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    iput-object p1, p0, Lw2/e$a$a;->b:Lw2/e$a;

    iput-object p2, p0, Lw2/e$a$a;->a:Lorg/json/JSONObject;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(Ljava/lang/String;)V
    .locals 3

    iget-object p1, p0, Lw2/e$a$a;->b:Lw2/e$a;

    iget-object p1, p1, Lw2/e$a;->b:Lw2/e;

    invoke-static {p1}, Lw2/e;->a(Lw2/e;)Ln4/e;

    move-result-object p1

    invoke-virtual {p1}, Ln4/e;->a()V

    iget-object p1, p0, Lw2/e$a$a;->b:Lw2/e$a;

    iget-object p1, p1, Lw2/e$a;->b:Lw2/e;

    invoke-static {p1}, Lw2/e;->f(Lw2/e;)Ls4/a;

    move-result-object p1

    iget-object v0, p0, Lw2/e$a$a;->b:Lw2/e$a;

    iget-object v0, v0, Lw2/e$a;->b:Lw2/e;

    invoke-static {v0}, Lw2/e;->f(Lw2/e;)Ls4/a;

    move-result-object v0

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    const-string v1, "No "

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object v1, p0, Lw2/e$a$a;->b:Lw2/e$a;

    iget-object v1, v1, Lw2/e$a;->b:Lw2/e;

    invoke-static {v1}, Lw2/e;->f(Lw2/e;)Ls4/a;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "transaction_id"

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v2, " defined"

    invoke-virtual {v0, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p1, v1, v0}, Ls4/a;->g(Ljava/lang/String;Ljava/lang/String;)V

    :try_start_0
    iget-object p1, p0, Lw2/e$a$a;->a:Lorg/json/JSONObject;

    const-string v0, "status"

    invoke-virtual {p1, v0}, Lorg/json/JSONObject;->getString(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    const-string v0, "Completed"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lw2/e$a$a;->b:Lw2/e$a;

    iget-object p1, p1, Lw2/e$a;->b:Lw2/e;

    invoke-static {p1}, Lw2/e;->g(Lw2/e;)Lv2/b;

    move-result-object p1

    const-string v0, "Purchased Video Course"

    const-string v1, "videos"

    const-string v2, "paid"

    invoke-interface {p1, v0, v1, v2}, Lv2/b;->q(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lw2/e$a$a;->b:Lw2/e$a;

    iget-object p1, p1, Lw2/e$a;->b:Lw2/e;

    invoke-static {p1}, Lw2/e;->g(Lw2/e;)Lv2/b;

    move-result-object p1

    invoke-interface {p1}, Lv2/b;->L0()V
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    move-exception p1

    invoke-virtual {p1}, Ljava/lang/Throwable;->printStackTrace()V

    :goto_0
    return-void
.end method

.method public onError(Ljava/lang/String;)V
    .locals 0

    iget-object p1, p0, Lw2/e$a$a;->b:Lw2/e$a;

    iget-object p1, p1, Lw2/e$a;->b:Lw2/e;

    invoke-static {p1}, Lw2/e;->a(Lw2/e;)Ln4/e;

    move-result-object p1

    invoke-virtual {p1}, Ln4/e;->a()V

    return-void
.end method
