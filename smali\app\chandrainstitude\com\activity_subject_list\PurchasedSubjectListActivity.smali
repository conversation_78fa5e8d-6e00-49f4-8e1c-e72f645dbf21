.class public Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;
.super Landroidx/appcompat/app/d;
.source "SourceFile"

# interfaces
.implements Lapp/chandrainstitude/com/activity_subject_list/a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$d;,
        Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$c;,
        Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$b;
    }
.end annotation


# instance fields
.field private O:Lk3/a;

.field private P:Lm4/a;

.field private Q:Ljava/lang/String;

.field private R:Ljava/lang/String;

.field private S:Ljava/lang/String;

.field private T:Ljava/lang/String;

.field private U:Ljava/lang/String;

.field private V:Ljava/lang/String;

.field private W:Ljava/lang/String;

.field private X:Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$d;

.field private Y:Lapp/chandrainstitude/com/activity_chapter_list/ChapterListActivity$d;

.field private Z:Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$c;

.field private a0:Landroid/widget/TextView;

.field private b0:Lcom/google/android/material/tabs/TabLayout;

.field private c0:Landroidx/viewpager/widget/ViewPager;

.field private d0:[Ljava/lang/String;

.field private e0:[Landroidx/fragment/app/Fragment;


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Landroidx/appcompat/app/d;-><init>()V

    return-void
.end method

.method static synthetic y2(Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;)[Landroidx/fragment/app/Fragment;
    .locals 0

    iget-object p0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->e0:[Landroidx/fragment/app/Fragment;

    return-object p0
.end method

.method static synthetic z2(Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;)[Ljava/lang/String;
    .locals 0

    iget-object p0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->d0:[Ljava/lang/String;

    return-object p0
.end method


# virtual methods
.method public A2()Lm4/a;
    .locals 1

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    return-object v0
.end method

.method public B2(Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$d;)V
    .locals 4

    sget-object v0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$a;->a:[I

    invoke-virtual {p1}, Ljava/lang/Enum;->ordinal()I

    move-result p1

    aget p1, v0, p1

    const/4 v0, 0x0

    const/4 v1, 0x0

    const/4 v2, 0x1

    packed-switch p1, :pswitch_data_0

    goto/16 :goto_0

    :pswitch_0
    const-string p1, "Syllabus"

    filled-new-array {p1}, [Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->d0:[Ljava/lang/String;

    new-array p1, v2, [Landroidx/fragment/app/Fragment;

    new-instance v2, La4/a;

    const-string v3, "syllabus"

    invoke-direct {v2, v3, v0}, La4/a;-><init>(Ljava/lang/String;La4/a$b;)V

    aput-object v2, p1, v1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->e0:[Landroidx/fragment/app/Fragment;

    goto/16 :goto_0

    :pswitch_1
    const-string p1, "MOCK TEST"

    filled-new-array {p1}, [Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->d0:[Ljava/lang/String;

    new-array p1, v2, [Landroidx/fragment/app/Fragment;

    new-instance v0, Ld4/o;

    iget-object v2, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->Q:Ljava/lang/String;

    invoke-static {v2}, Ljava/lang/Integer;->parseInt(Ljava/lang/String;)I

    move-result v2

    invoke-direct {v0, v2}, Ld4/o;-><init>(I)V

    aput-object v0, p1, v1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->e0:[Landroidx/fragment/app/Fragment;

    goto/16 :goto_0

    :pswitch_2
    const-string p1, "Lecture Notes"

    filled-new-array {p1}, [Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->d0:[Ljava/lang/String;

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->Z:Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$c;

    sget-object v3, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$c;->a:Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$c;

    if-ne p1, v3, :cond_0

    new-array p1, v2, [Landroidx/fragment/app/Fragment;

    new-instance v0, Li3/c;

    iget-object v2, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->Y:Lapp/chandrainstitude/com/activity_chapter_list/ChapterListActivity$d;

    invoke-direct {v0, v2}, Li3/c;-><init>(Lapp/chandrainstitude/com/activity_chapter_list/ChapterListActivity$d;)V

    aput-object v0, p1, v1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->e0:[Landroidx/fragment/app/Fragment;

    goto/16 :goto_0

    :cond_0
    new-array p1, v2, [Landroidx/fragment/app/Fragment;

    new-instance v2, La4/a;

    const-string v3, "lecture"

    invoke-direct {v2, v3, v0}, La4/a;-><init>(Ljava/lang/String;La4/a$b;)V

    aput-object v2, p1, v1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->e0:[Landroidx/fragment/app/Fragment;

    goto :goto_0

    :pswitch_3
    const-string p1, "Practice Set"

    filled-new-array {p1}, [Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->d0:[Ljava/lang/String;

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->Z:Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$c;

    sget-object v3, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$c;->a:Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$c;

    if-ne p1, v3, :cond_1

    new-array p1, v2, [Landroidx/fragment/app/Fragment;

    new-instance v0, Li3/c;

    iget-object v2, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->Y:Lapp/chandrainstitude/com/activity_chapter_list/ChapterListActivity$d;

    invoke-direct {v0, v2}, Li3/c;-><init>(Lapp/chandrainstitude/com/activity_chapter_list/ChapterListActivity$d;)V

    aput-object v0, p1, v1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->e0:[Landroidx/fragment/app/Fragment;

    goto :goto_0

    :cond_1
    new-array p1, v2, [Landroidx/fragment/app/Fragment;

    new-instance v2, La4/a;

    const-string v3, "practice"

    invoke-direct {v2, v3, v0}, La4/a;-><init>(Ljava/lang/String;La4/a$b;)V

    aput-object v2, p1, v1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->e0:[Landroidx/fragment/app/Fragment;

    goto :goto_0

    :pswitch_4
    const-string p1, "Model Paper"

    filled-new-array {p1}, [Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->d0:[Ljava/lang/String;

    new-array p1, v2, [Landroidx/fragment/app/Fragment;

    new-instance v2, La4/a;

    const-string v3, "model"

    invoke-direct {v2, v3, v0}, La4/a;-><init>(Ljava/lang/String;La4/a$b;)V

    aput-object v2, p1, v1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->e0:[Landroidx/fragment/app/Fragment;

    goto :goto_0

    :pswitch_5
    const-string p1, "Live Lectures"

    filled-new-array {p1}, [Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->d0:[Ljava/lang/String;

    new-array p1, v2, [Landroidx/fragment/app/Fragment;

    new-instance v0, Li3/a;

    invoke-direct {v0}, Li3/a;-><init>()V

    aput-object v0, p1, v1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->e0:[Landroidx/fragment/app/Fragment;

    goto :goto_0

    :pswitch_6
    const-string p1, "Lectures"

    filled-new-array {p1}, [Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->d0:[Ljava/lang/String;

    new-array p1, v2, [Landroidx/fragment/app/Fragment;

    new-instance v0, Li3/c;

    iget-object v2, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->Y:Lapp/chandrainstitude/com/activity_chapter_list/ChapterListActivity$d;

    invoke-direct {v0, v2}, Li3/c;-><init>(Lapp/chandrainstitude/com/activity_chapter_list/ChapterListActivity$d;)V

    aput-object v0, p1, v1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->e0:[Landroidx/fragment/app/Fragment;

    :goto_0
    return-void

    nop

    :pswitch_data_0
    .packed-switch 0x1
        :pswitch_6
        :pswitch_5
        :pswitch_4
        :pswitch_3
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public i(Ljava/lang/String;)V
    .locals 2

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->a0:Landroid/widget/TextView;

    const/4 v1, 0x0

    invoke-virtual {v0, v1}, Landroid/view/View;->setVisibility(I)V

    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x18

    if-lt v0, v1, :cond_0

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->a0:Landroid/widget/TextView;

    const/16 v1, 0x3f

    invoke-static {p1, v1}, Lv1/a;->a(Ljava/lang/String;I)Landroid/text/Spanned;

    move-result-object p1

    goto :goto_0

    :cond_0
    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->a0:Landroid/widget/TextView;

    invoke-static {p1}, Landroid/text/Html;->fromHtml(Ljava/lang/String;)Landroid/text/Spanned;

    move-result-object p1

    :goto_0
    invoke-virtual {v0, p1}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->a0:Landroid/widget/TextView;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Landroid/widget/TextView;->setSelected(Z)V

    return-void
.end method

.method protected onCreate(Landroid/os/Bundle;)V
    .locals 2

    invoke-super {p0, p1}, Landroidx/fragment/app/h;->onCreate(Landroid/os/Bundle;)V

    const p1, 0x7f0d003c

    invoke-virtual {p0, p1}, Landroidx/appcompat/app/d;->setContentView(I)V

    new-instance p1, Lk3/c;

    invoke-direct {p1, p0, p0}, Lk3/c;-><init>(Lapp/chandrainstitude/com/activity_subject_list/a;Landroid/content/Context;)V

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->O:Lk3/a;

    const p1, 0x7f0a0365

    invoke-virtual {p0, p1}, Landroidx/appcompat/app/d;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Landroid/widget/TextView;

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->a0:Landroid/widget/TextView;

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "screen_name"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->V:Ljava/lang/String;

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "course_id"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->Q:Ljava/lang/String;

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "subject_id"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->R:Ljava/lang/String;

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "amount"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->S:Ljava/lang/String;

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "notice"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->U:Ljava/lang/String;

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "course_name"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->T:Ljava/lang/String;

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "page_name"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getStringExtra(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p1

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->W:Ljava/lang/String;

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "ShowPurchasedCourse"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getSerializableExtra(Ljava/lang/String;)Ljava/io/Serializable;

    move-result-object p1

    check-cast p1, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$d;

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->X:Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$d;

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "LECTURE_TYPE"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getSerializableExtra(Ljava/lang/String;)Ljava/io/Serializable;

    move-result-object p1

    check-cast p1, Lapp/chandrainstitude/com/activity_chapter_list/ChapterListActivity$d;

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->Y:Lapp/chandrainstitude/com/activity_chapter_list/ChapterListActivity$d;

    invoke-virtual {p0}, Landroid/app/Activity;->getIntent()Landroid/content/Intent;

    move-result-object p1

    const-string v0, "PreviousActivity"

    invoke-virtual {p1, v0}, Landroid/content/Intent;->getSerializableExtra(Ljava/lang/String;)Ljava/io/Serializable;

    move-result-object p1

    check-cast p1, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$c;

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->Z:Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$c;

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->X:Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$d;

    invoke-virtual {p0, p1}, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->B2(Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$d;)V

    invoke-virtual {p0}, Landroidx/appcompat/app/d;->l2()Landroidx/appcompat/app/a;

    move-result-object p1

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->V:Ljava/lang/String;

    invoke-virtual {p1, v0}, Landroidx/appcompat/app/a;->x(Ljava/lang/CharSequence;)V

    new-instance p1, Lm4/a;

    invoke-direct {p1}, Lm4/a;-><init>()V

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->Q:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lm4/a;->l(Ljava/lang/String;)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->R:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lm4/a;->r(Ljava/lang/String;)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->S:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lm4/a;->k(Ljava/lang/String;)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->U:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lm4/a;->n(Ljava/lang/String;)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->T:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lm4/a;->m(Ljava/lang/String;)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->V:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lm4/a;->p(Ljava/lang/String;)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->X:Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$d;

    invoke-virtual {p1, v0}, Lm4/a;->q(Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$d;)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->W:Ljava/lang/String;

    invoke-virtual {p1, v0}, Lm4/a;->o(Ljava/lang/String;)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->S:Ljava/lang/String;

    const-string v0, "0"

    invoke-virtual {p1, v0}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    const-string v0, "purchased"

    invoke-virtual {p1, v0}, Lm4/a;->t(Ljava/lang/String;)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->O:Lk3/a;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->U:Ljava/lang/String;

    invoke-interface {p1, v0}, Lk3/a;->i(Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->P:Lm4/a;

    const-string v0, "demo"

    invoke-virtual {p1, v0}, Lm4/a;->t(Ljava/lang/String;)V

    :goto_0
    const p1, 0x7f0a03bf

    invoke-virtual {p0, p1}, Landroidx/appcompat/app/d;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Landroidx/viewpager/widget/ViewPager;

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->c0:Landroidx/viewpager/widget/ViewPager;

    const/4 v0, 0x1

    invoke-virtual {p1, v0}, Landroidx/viewpager/widget/ViewPager;->setOffscreenPageLimit(I)V

    iget-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->c0:Landroidx/viewpager/widget/ViewPager;

    new-instance v0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$b;

    invoke-virtual {p0}, Landroidx/fragment/app/h;->Z1()Landroidx/fragment/app/FragmentManager;

    move-result-object v1

    invoke-direct {v0, p0, v1}, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity$b;-><init>(Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;Landroidx/fragment/app/FragmentManager;)V

    invoke-virtual {p1, v0}, Landroidx/viewpager/widget/ViewPager;->setAdapter(Landroidx/viewpager/widget/a;)V

    const p1, 0x7f0a030e

    invoke-virtual {p0, p1}, Landroidx/appcompat/app/d;->findViewById(I)Landroid/view/View;

    move-result-object p1

    check-cast p1, Lcom/google/android/material/tabs/TabLayout;

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->b0:Lcom/google/android/material/tabs/TabLayout;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_subject_list/PurchasedSubjectListActivity;->c0:Landroidx/viewpager/widget/ViewPager;

    invoke-virtual {p1, v0}, Lcom/google/android/material/tabs/TabLayout;->setupWithViewPager(Landroidx/viewpager/widget/ViewPager;)V

    return-void
.end method

.method public onOptionsItemSelected(Landroid/view/MenuItem;)Z
    .locals 1

    invoke-interface {p1}, Landroid/view/MenuItem;->getItemId()I

    move-result p1

    const v0, 0x102002c

    if-eq p1, v0, :cond_0

    goto :goto_0

    :cond_0
    invoke-super {p0}, Landroidx/activity/ComponentActivity;->onBackPressed()V

    :goto_0
    const/4 p1, 0x1

    return p1
.end method
