.class public Lp4/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lp4/a;


# instance fields
.field private a:Lo4/a;

.field private b:Landroid/content/Context;

.field private c:Ln4/e;

.field private d:Ls4/a;

.field private e:Ljava/util/ArrayList;
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "Ljava/util/ArrayList<",
            "Lm4/p;",
            ">;"
        }
    .end annotation
.end field


# direct methods
.method public constructor <init>(Lo4/a;Landroid/content/Context;)V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    iput-object p1, p0, Lp4/b;->a:Lo4/a;

    iput-object p2, p0, Lp4/b;->b:Landroid/content/Context;

    invoke-static {}, Lapp/chandrainstitude/com/networking/AppController;->m()Ls4/a;

    move-result-object p1

    iput-object p1, p0, Lp4/b;->d:Ls4/a;

    new-instance p1, Ln4/e;

    invoke-direct {p1, p2}, Ln4/e;-><init>(Landroid/content/Context;)V

    iput-object p1, p0, Lp4/b;->c:Ln4/e;

    return-void
.end method

.method static synthetic f(Lp4/b;)Lo4/a;
    .locals 0

    iget-object p0, p0, Lp4/b;->a:Lo4/a;

    return-object p0
.end method

.method static synthetic g(Lp4/b;)Ljava/util/ArrayList;
    .locals 0

    iget-object p0, p0, Lp4/b;->e:Ljava/util/ArrayList;

    return-object p0
.end method

.method static synthetic h(Lp4/b;Ljava/util/ArrayList;)Ljava/util/ArrayList;
    .locals 0

    iput-object p1, p0, Lp4/b;->e:Ljava/util/ArrayList;

    return-object p1
.end method

.method static synthetic j(Lp4/b;)Ln4/e;
    .locals 0

    iget-object p0, p0, Lp4/b;->c:Ln4/e;

    return-object p0
.end method

.method static synthetic k(Lp4/b;)Landroid/content/Context;
    .locals 0

    iget-object p0, p0, Lp4/b;->b:Landroid/content/Context;

    return-object p0
.end method


# virtual methods
.method public a()V
    .locals 2

    iget-object v0, p0, Lp4/b;->e:Ljava/util/ArrayList;

    if-eqz v0, :cond_0

    invoke-static {v0}, Ljava/util/Collections;->reverse(Ljava/util/List;)V

    iget-object v0, p0, Lp4/b;->a:Lo4/a;

    iget-object v1, p0, Lp4/b;->e:Ljava/util/ArrayList;

    invoke-interface {v0, v1}, Lo4/a;->d(Ljava/util/ArrayList;)V

    :cond_0
    return-void
.end method

.method public b(Ljava/lang/String;)V
    .locals 4

    :try_start_0
    invoke-virtual {p1}, Ljava/lang/String;->trim()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v0}, Ljava/lang/String;->isEmpty()Z

    move-result v0

    if-nez v0, :cond_2

    iget-object v0, p0, Lp4/b;->e:Ljava/util/ArrayList;

    if-eqz v0, :cond_2

    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result v0

    if-lez v0, :cond_2

    new-instance v0, Ljava/util/ArrayList;

    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    const/4 v1, 0x0

    :goto_0
    iget-object v2, p0, Lp4/b;->e:Ljava/util/ArrayList;

    invoke-virtual {v2}, Ljava/util/ArrayList;->size()I

    move-result v2

    if-ge v1, v2, :cond_1

    iget-object v2, p0, Lp4/b;->e:Ljava/util/ArrayList;

    invoke-virtual {v2, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lm4/p;

    invoke-virtual {v2}, Lm4/p;->s()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v2}, Ljava/lang/String;->toLowerCase()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {p1}, Ljava/lang/String;->toLowerCase()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v2, v3}, Ljava/lang/String;->contains(Ljava/lang/CharSequence;)Z

    move-result v2

    if-eqz v2, :cond_0

    iget-object v2, p0, Lp4/b;->e:Ljava/util/ArrayList;

    invoke-virtual {v2, v1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object v2

    check-cast v2, Lm4/p;

    invoke-virtual {v0, v2}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z

    :cond_0
    add-int/lit8 v1, v1, 0x1

    goto :goto_0

    :cond_1
    invoke-virtual {v0}, Ljava/util/ArrayList;->size()I

    move-result p1

    if-lez p1, :cond_3

    iget-object p1, p0, Lp4/b;->a:Lo4/a;

    invoke-interface {p1, v0}, Lo4/a;->u(Ljava/util/ArrayList;)V

    goto :goto_1

    :cond_2
    iget-object p1, p0, Lp4/b;->a:Lo4/a;

    iget-object v0, p0, Lp4/b;->e:Ljava/util/ArrayList;

    invoke-interface {p1, v0}, Lo4/a;->d(Ljava/util/ArrayList;)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    :catch_0
    :cond_3
    :goto_1
    return-void
.end method

.method public c(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 10

    const-string p3, "videos"

    invoke-virtual {p1, p3}, Ljava/lang/String;->equalsIgnoreCase(Ljava/lang/String;)Z

    move-result p1

    if-eqz p1, :cond_0

    iget-object v0, p0, Lp4/b;->a:Lo4/a;

    const-class v1, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;

    const-string v3, "0"

    move-object v2, p2

    move-object v4, p4

    move-object v5, p5

    invoke-interface/range {v0 .. v5}, Lo4/a;->g(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_0
    iget-object v4, p0, Lp4/b;->a:Lo4/a;

    const-class v5, Lapp/chandrainstitude/com/activity_notes_list/NotesListActivity;

    const-string v7, "0"

    move-object v6, p2

    move-object v8, p4

    move-object v9, p5

    invoke-interface/range {v4 .. v9}, Lo4/a;->g(Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    :goto_0
    return-void
.end method

.method public d(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    iget-object p5, p0, Lp4/b;->c:Ln4/e;

    invoke-virtual {p5}, Ln4/e;->c()V

    :try_start_0
    new-instance p5, Lorg/json/JSONObject;

    invoke-direct {p5}, Lorg/json/JSONObject;-><init>()V

    const-string v0, "order_id"

    invoke-virtual {p5, v0, p1}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string v0, "payment_id"

    invoke-virtual {p5, v0, p2}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string p2, "student_name"

    iget-object v0, p0, Lp4/b;->d:Ls4/a;

    invoke-static {v0}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "full_name"

    invoke-virtual {v0, v1}, Ls4/a;->d(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v0

    invoke-virtual {p5, p2, v0}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string p2, "course_name"

    invoke-virtual {p5, p2, p3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string p2, "course_mode"

    invoke-virtual {p5, p2, p4}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string p2, "mobile_number"

    iget-object p3, p0, Lp4/b;->d:Ls4/a;

    invoke-static {p3}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    const-string p4, "mobile"

    invoke-virtual {p3, p4}, Ls4/a;->d(Ljava/lang/String;)Ljava/lang/String;

    move-result-object p3

    invoke-virtual {p5, p2, p3}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string p2, "branch_name"

    invoke-virtual {p5, p2, p6}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string p2, "created_date"

    invoke-virtual {p5, p2, p7}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    const-string p2, "amount"

    invoke-virtual {p5, p2, p8}, Lorg/json/JSONObject;->put(Ljava/lang/String;Ljava/lang/Object;)Lorg/json/JSONObject;

    invoke-static {}, Lapp/chandrainstitude/com/networking/AppController;->p()Ln4/h;

    move-result-object p2

    invoke-static {}, Ln4/c;->A()Ljava/lang/String;

    move-result-object p3

    const/4 p4, 0x1

    invoke-virtual {p5, p4}, Lorg/json/JSONObject;->toString(I)Ljava/lang/String;

    move-result-object p4

    const-string p5, "downloadPDFReceipt"

    new-instance p6, Lp4/b$b;

    invoke-direct {p6, p0, p1}, Lp4/b$b;-><init>(Lp4/b;Ljava/lang/String;)V

    invoke-virtual {p2, p3, p4, p5, p6}, Ln4/h;->b(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ln4/f;)V
    :try_end_0
    .catch Lorg/json/JSONException; {:try_start_0 .. :try_end_0} :catch_0

    goto :goto_0

    :catch_0
    iget-object p1, p0, Lp4/b;->c:Ln4/e;

    invoke-virtual {p1}, Ln4/e;->a()V

    :goto_0
    return-void
.end method

.method public e(Ljava/lang/String;Ljava/lang/String;)V
    .locals 2

    invoke-static {}, Lapp/chandrainstitude/com/networking/AppController;->o()Ln4/g;

    move-result-object p2

    new-instance v0, Ljava/lang/StringBuilder;

    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    sget-object v1, Ln4/c;->e:Ljava/lang/String;

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v1, "courses/list/purchased?type="

    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string p1, "&userId="

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    iget-object p1, p0, Lp4/b;->d:Ls4/a;

    invoke-static {p1}, Ljava/util/Objects;->requireNonNull(Ljava/lang/Object;)Ljava/lang/Object;

    const-string v1, "u_id"

    invoke-virtual {p1, v1}, Ls4/a;->c(Ljava/lang/String;)I

    move-result p1

    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object p1

    new-instance v0, Lp4/b$a;

    invoke-direct {v0, p0}, Lp4/b$a;-><init>(Lp4/b;)V

    const-string v1, "getUserPurchasedList"

    invoke-virtual {p2, p1, v1, v0}, Ln4/g;->b(Ljava/lang/String;Ljava/lang/String;Ln4/f;)V

    return-void
.end method

.method public i(Ljava/lang/String;)V
    .locals 1

    iget-object v0, p0, Lp4/b;->a:Lo4/a;

    invoke-interface {v0, p1}, Lo4/a;->i(Ljava/lang/String;)V

    return-void
.end method
