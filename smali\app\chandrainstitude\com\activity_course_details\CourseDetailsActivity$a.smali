.class Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lw3/e0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;->W(Ljava/util/ArrayList;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# instance fields
.field final synthetic a:Ljava/util/ArrayList;

.field final synthetic b:Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;


# direct methods
.method constructor <init>(Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;Ljava/util/ArrayList;)V
    .locals 0
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "()V"
        }
    .end annotation

    iput-object p1, p0, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity$a;->b:Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;

    iput-object p2, p0, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity$a;->a:Ljava/util/ArrayList;

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public a(ILjava/lang/String;)V
    .locals 3

    invoke-virtual {p2}, Ljava/lang/String;->hashCode()I

    const-string v0, "NOT PURCHASED"

    invoke-virtual {p2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v0

    if-nez v0, :cond_1

    const-string v0, "TAG"

    invoke-virtual {p2, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result p2

    if-nez p2, :cond_0

    goto :goto_0

    :cond_0
    iget-object p2, p0, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity$a;->b:Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;

    invoke-static {p2}, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;->B2(Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;)Lw1/b;

    move-result-object p2

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity$a;->a:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lm4/h;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity$a;->b:Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;

    invoke-static {v0}, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;->y2(Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;)Ljava/lang/String;

    move-result-object v0

    iget-object v1, p0, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity$a;->b:Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;

    invoke-static {v1}, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;->z2(Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;)Ljava/lang/String;

    move-result-object v1

    iget-object v2, p0, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity$a;->b:Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;

    invoke-static {v2}, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;->A2(Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;)Ljava/lang/String;

    move-result-object v2

    invoke-interface {p2, p1, v0, v1, v2}, Lw1/b;->c(Lm4/h;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    goto :goto_0

    :cond_1
    iget-object p2, p0, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity$a;->b:Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;

    iget-object v0, p0, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity$a;->a:Ljava/util/ArrayList;

    invoke-virtual {v0, p1}, Ljava/util/ArrayList;->get(I)Ljava/lang/Object;

    move-result-object p1

    check-cast p1, Lm4/h;

    invoke-virtual {p1}, Lm4/h;->d()Ljava/lang/String;

    move-result-object p1

    invoke-virtual {p2, p1}, Lapp/chandrainstitude/com/activity_course_details/CourseDetailsActivity;->H2(Ljava/lang/String;)V

    :goto_0
    return-void
.end method
